<!DOCTYPE html>
<html>
<head>
    <title>Murder Agent Backend Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background-color: #0056b3; }
        #results { margin-top: 20px; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Murder Agent Backend Test</h1>
    
    <div class="info">
        <strong>Testing Backend Connectivity</strong><br>
        This page will test if the Murder Agent backend is running and accessible.
    </div>
    
    <button onclick="testBackend()">Test Backend Connection</button>
    <button onclick="testMurderAgent()">Test Murder Agent Endpoint</button>
    <button onclick="testHealthCheck()">Test Health Check</button>
    <button onclick="startNewSession()">Start New Murder Agent Session</button>
    
    <div id="results"></div>

    <script>
        const resultsDiv = document.getElementById('results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            resultsDiv.appendChild(div);
        }
        
        function addJsonResult(data) {
            const pre = document.createElement('pre');
            pre.textContent = JSON.stringify(data, null, 2);
            resultsDiv.appendChild(pre);
        }
        
        async function testBackend() {
            resultsDiv.innerHTML = '';
            addResult('Testing backend connection...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✓ Backend is running!', 'success');
                    addJsonResult(data);
                } else {
                    addResult(`✗ Backend responded with status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`✗ Backend connection failed: ${error.message}`, 'error');
                addResult('Make sure the Python backend server is running on port 5000', 'info');
            }
        }
        
        async function testHealthCheck() {
            resultsDiv.innerHTML = '';
            addResult('Testing health check endpoint...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/api/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✓ Health check successful!', 'success');
                    addJsonResult(data);
                } else {
                    addResult(`✗ Health check failed with status: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult(`✗ Health check failed: ${error.message}`, 'error');
            }
        }
        
        async function testMurderAgent() {
            resultsDiv.innerHTML = '';
            addResult('Testing Murder Agent endpoint...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/api/augment/murder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        question: "ping"
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✓ Murder Agent endpoint is working!', 'success');
                    addJsonResult(data);
                } else {
                    addResult(`✗ Murder Agent endpoint failed with status: ${response.status}`, 'error');
                    const errorText = await response.text();
                    addResult(`Error details: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`✗ Murder Agent endpoint failed: ${error.message}`, 'error');
            }
        }
        
        async function startNewSession() {
            resultsDiv.innerHTML = '';
            addResult('Starting new Murder Agent session...', 'info');
            
            try {
                const response = await fetch('http://localhost:5000/api/augment/murder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        question: "FORCE_NEW_SESSION"
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult('✓ New Murder Agent session started!', 'success');
                    addJsonResult(data);
                } else {
                    addResult(`✗ Failed to start new session with status: ${response.status}`, 'error');
                    const errorText = await response.text();
                    addResult(`Error details: ${errorText}`, 'error');
                }
            } catch (error) {
                addResult(`✗ Failed to start new session: ${error.message}`, 'error');
            }
        }
        
        // Auto-test on page load
        window.onload = function() {
            testBackend();
        };
    </script>
</body>
</html>
