<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Incident Report - {{ incident.id }}</title>
    <style>
        body { 
            font-family: Arial, sans-serif;
            margin: 2cm;
            line-height: 1.5;
        }
        h1 { 
            color: #003366;
            border-bottom: 1px solid #003366;
            padding-bottom: 10px;
        }
        h2 {
            color: #003366;
            margin-top: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 20px;
        }
        .info-row {
            display: flex;
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            width: 200px;
        }
        .info-value {
            flex: 1;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 0.8em;
            color: #666;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            font-weight: bold;
            color: white;
            font-size: 14px;
        }
        .status-open {
            background-color: #f39c12;
        }
        .status-closed {
            background-color: #27ae60;
        }
        .status-investigation {
            background-color: #3498db;
        }
        .status-pending {
            background-color: #e74c3c;
        }
        .status-resolved {
            background-color: #2ecc71;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>INCIDENT INVESTIGATION REPORT</h1>
        <p>Report ID: {{ incident.id }}</p>
        <p>Generated on: {{ now }}</p>
    </div>

    <div class="section">
        <h2>Incident Information</h2>
        <table>
            <tr>
                <th>Field</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Date</td>
                <td>{{ incident.date }}</td>
            </tr>
            <tr>
                <td>Time</td>
                <td>{{ incident.time }}</td>
            </tr>
            <tr>
                <td>Location</td>
                <td>{{ incident.location }}</td>
            </tr>
            <tr>
                <td>Incident Type</td>
                <td>{{ incident.incident_type }}</td>
            </tr>
            <tr>
                <td>Reporting Officer</td>
                <td>{{ incident.reporting_officer }}</td>
            </tr>
            <tr>
                <td>Status</td>
                <td>
                    <span class="status-badge status-{{ incident.status.lower().replace(' ', '-') }}">
                        {{ incident.status }}
                    </span>
                </td>
            </tr>
        </table>
    </div>

    {% if incident.victims %}
    <div class="section">
        <h2>Victims</h2>
        {% for victim in incident.victims %}
        <h3>Victim {{ loop.index }}</h3>
        <table>
            <tr>
                <th>Field</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Name</td>
                <td>{{ victim.name }}</td>
            </tr>
            <tr>
                <td>Age</td>
                <td>{{ victim.age }}</td>
            </tr>
            <tr>
                <td>Gender</td>
                <td>{{ victim.gender }}</td>
            </tr>
            <tr>
                <td>Injuries</td>
                <td>{{ victim.injuries }}</td>
            </tr>
        </table>
        {% endfor %}
    </div>
    {% endif %}

    {% if incident.suspects %}
    <div class="section">
        <h2>Suspects</h2>
        {% for suspect in incident.suspects %}
        <h3>Suspect {{ loop.index }}</h3>
        <table>
            <tr>
                <th>Field</th>
                <th>Value</th>
            </tr>
            <tr>
                <td>Name</td>
                <td>{{ suspect.name }}</td>
            </tr>
            <tr>
                <td>Age</td>
                <td>{{ suspect.age }}</td>
            </tr>
            <tr>
                <td>Gender</td>
                <td>{{ suspect.gender }}</td>
            </tr>
            <tr>
                <td>Description</td>
                <td>{{ suspect.description }}</td>
            </tr>
        </table>
        {% endfor %}
    </div>
    {% endif %}

    <div class="section">
        <h2>Description</h2>
        <p>{{ incident.description }}</p>
    </div>

    <div class="section">
        <h2>Evidence</h2>
        <p>{{ incident.evidence }}</p>
    </div>

    <div class="footer">
        <p>Generated by AI Justice Grid Investigation System</p>
        <p>{{ now }}</p>
    </div>
</body>
</html>
