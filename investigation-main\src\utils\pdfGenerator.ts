/* eslint-disable */
import { jsPDF } from 'jspdf';
import { InvestigationReport, InvestigationQuestion } from '@/app/types';

/**
 * Clean markdown formatting from text to produce plain text output
 * @param text Text that may contain markdown formatting
 * @returns Clean plain text without markdown formatting
 */
export const cleanMarkdownText = (text: string): string => {
  if (!text) {
    return "";
  }

  // Convert to string and strip
  let cleanText = text.toString().trim();

  // Remove markdown formatting characters
  cleanText = cleanText.replace(/\*\*/g, ''); // Bold markers
  cleanText = cleanText.replace(/\*/g, '');   // Italic markers
  cleanText = cleanText.replace(/__/g, '');   // Alternative bold
  cleanText = cleanText.replace(/_/g, '');    // Alternative italic

  // Remove header markers
  cleanText = cleanText.replace(/###/g, '');
  cleanText = cleanText.replace(/##/g, '');
  cleanText = cleanText.replace(/#/g, '');

  // Clean bullet points and list markers
  cleanText = cleanText.replace(/- /g, '');
  cleanText = cleanText.replace(/\+ /g, '');
  cleanText = cleanText.replace(/\* /g, '');

  // Remove extra whitespace and normalize
  cleanText = cleanText.split(/\s+/).join(' ').trim();

  return cleanText;
};

/**
 * Generate a PDF document from an investigation report
 * @param report The investigation report to convert to PDF
 * @returns The generated PDF document
 */
export const generateInvestigationReportPDF = (report: InvestigationReport): jsPDF => {
  // Create a new PDF document
  const doc = new jsPDF();

  // Set initial position
  let yPos = 20;
  const margin = 20;
  const pageWidth = doc.internal.pageSize.getWidth();
  const contentWidth = pageWidth - (margin * 2);

  // Add title
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text(report.title, pageWidth / 2, yPos, { align: 'center' });
  yPos += 10;

  // Add metadata
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  doc.text(`Investigation ID: ${report.investigationId}`, margin, yPos);
  yPos += 6;
  doc.text(`Type: ${report.investigationType}`, margin, yPos);
  yPos += 6;
  doc.text(`Date: ${report.createdDate}`, margin, yPos);
  yPos += 6;
  doc.text(`Generated by: ${report.createdBy}`, margin, yPos);
  yPos += 15;

  // Add section divider
  doc.setDrawColor(200, 200, 200);
  doc.line(margin, yPos - 5, pageWidth - margin, yPos - 5);

  // Add questions and answers section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('Investigation Questions & Answers', margin, yPos);
  yPos += 10;

  // Add each question and answer
  if (report.questions && report.questions.length > 0) {
    doc.setFontSize(11);

    report.questions.forEach((qa: InvestigationQuestion, index: number) => {
      // Check if we need a new page
      if (yPos > doc.internal.pageSize.getHeight() - 30) {
        doc.addPage();
        yPos = 20;
      }

      // Add question
      doc.setFont('helvetica', 'bold');
      const cleanQuestion = cleanMarkdownText(qa.question);
      const questionText = `Q${index + 1}: ${cleanQuestion}`;
      const questionLines = doc.splitTextToSize(questionText, contentWidth);
      doc.text(questionLines, margin, yPos);
      yPos += (questionLines.length * 6);

      // Add answer
      doc.setFont('helvetica', 'normal');
      const cleanAnswer = cleanMarkdownText(qa.answer);
      const answerText = `A: ${cleanAnswer}`;
      const answerLines = doc.splitTextToSize(answerText, contentWidth);
      doc.text(answerLines, margin, yPos);
      yPos += (answerLines.length * 6) + 5;
    });
  } else {
    doc.setFontSize(11);
    doc.setFont('helvetica', 'italic');
    doc.text('No questions recorded for this investigation.', margin, yPos);
    yPos += 10;
  }

  // Add section divider
  doc.setDrawColor(200, 200, 200);
  doc.line(margin, yPos, pageWidth - margin, yPos);
  yPos += 10;

  // Check if we need a new page for the analysis
  if (yPos > doc.internal.pageSize.getHeight() - 60) {
    doc.addPage();
    yPos = 20;
  }

  // Add analysis section
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('Analysis', margin, yPos);
  yPos += 10;

  // Add analysis content
  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');

  // Clean up the analysis text to remove all markdown formatting
  const cleanAnalysis = cleanMarkdownText(report.analysis);

  const analysisLines = doc.splitTextToSize(cleanAnalysis, contentWidth);
  doc.text(analysisLines, margin, yPos);

  // Add footer
  const footerText = `Investigation Report - Generated on ${new Date().toLocaleDateString()}`;
  doc.setFontSize(8);
  doc.setTextColor(100, 100, 100);
  doc.text(footerText, pageWidth / 2, doc.internal.pageSize.getHeight() - 10, { align: 'center' });

  return doc;
};

/**
 * Download a PDF document with a given filename
 * @param doc The PDF document to download
 * @param filename The filename for the downloaded PDF
 */
export const downloadPDF = (doc: jsPDF, filename: string): void => {
  doc.save(filename);
};
