/* eslint-disable */
'use client';

import React, { useState } from 'react';
import { InvestigationReport } from '@/app/types';
import Card, { CardHeader, CardTitle, CardContent } from '@/app/components/ui/Card';
import Button from '@/app/components/ui/Button';
import { generateInvestigationReportPDF, downloadPDF } from '@/utils/pdfGenerator';
import {
  DocumentTextIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ClockIcon,
  CheckCircleIcon,
  ArrowDownIcon,
} from '@heroicons/react/24/outline';

interface InvestigationReportPanelProps {
  report: InvestigationReport;
  expanded?: boolean;
}

const InvestigationReportPanel: React.FC<InvestigationReportPanelProps> = ({
  report,
  expanded = false
}) => {
  const [isExpanded, setIsExpanded] = useState(expanded);

  // Format the date for display
  const formattedDate = new Date(report.createdDate).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });

  // Handle download PDF button click
  const handleDownloadPDF = () => {
    const doc = generateInvestigationReportPDF(report);
    downloadPDF(doc, `investigation-report-${report.id}.pdf`);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between bg-white pb-2">
        <div className="flex items-center">
          <DocumentTextIcon className="h-5 w-5 text-blue-600 mr-2" />
          <CardTitle className="text-lg">{report.title}</CardTitle>
        </div>

        <div className="flex items-center space-x-2">
          {report.status === 'completed' ? (
            <span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 text-xs font-medium text-green-800">
              <CheckCircleIcon className="mr-1 h-3 w-3" />
              Completed
            </span>
          ) : (
            <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
              <ClockIcon className="mr-1 h-3 w-3" />
              In Progress
            </span>
          )}

          <Button
            size="sm"
            variant="outline"
            onClick={handleDownloadPDF}
            className="ml-2"
          >
            <ArrowDownIcon className="mr-1 h-4 w-4" />
            Download PDF
          </Button>

          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="ml-2 rounded-full p-1 hover:bg-gray-100"
          >
            {isExpanded ? (
              <ChevronUpIcon className="h-5 w-5 text-gray-500" />
            ) : (
              <ChevronDownIcon className="h-5 w-5 text-gray-500" />
            )}
          </button>
        </div>
      </CardHeader>

      <div className="px-6 py-2 border-t border-gray-100">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center">
            <span className="mr-4">ID: {report.id}</span>
            <span className="mr-4">Type: {report.investigationType}</span>
          </div>
          <div>
            <span>Created: {formattedDate}</span>
          </div>
        </div>
      </div>

      {isExpanded && (
        <CardContent className="border-t border-gray-200 bg-gray-50 pt-4">
          <div className="mb-4">
            <p className="text-sm text-gray-500">
              Generated by: <span className="font-medium text-gray-700">{report.createdBy}</span>
            </p>
            <p className="text-sm text-gray-500">
              Investigation ID: <span className="font-medium text-gray-700">{report.investigationId}</span>
            </p>
          </div>

          <h4 className="font-medium text-gray-700 mb-3">Questions & Answers</h4>
          <div className="space-y-4">
            {report.questions.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No questions available</p>
            ) : (
              report.questions.map((qa, index) => (
                <div key={index} className="bg-white p-4 rounded-md border border-gray-200">
                  <p className="font-medium text-gray-800 mb-2">Q: {qa.question}</p>
                  <p className="text-gray-600">A: {qa.answer}</p>
                </div>
              ))
            )}
          </div>

          <h4 className="font-medium text-gray-700 mt-6 mb-3">Analysis</h4>
          <div className="bg-white p-4 rounded-md border border-gray-200">
            <p className="text-gray-600 whitespace-pre-line">{report.analysis}</p>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default InvestigationReportPanel;
