# Generated Investigation Reports

This directory contains sample HTML reports generated by the AI Justice Grid Investigation System. These reports demonstrate the new PDF generation capabilities.

## Sample Reports

### 1. `incident_report_INC-0001.html` - Trespassing Case
- **Type:** Trespassing incident
- **Status:** Closed
- **Features:** Single victim, single suspect, basic incident structure
- **Demonstrates:** Standard incident report format

### 2. `incident_report_INC-0008.html` - Domestic Dispute
- **Type:** Domestic dispute
- **Status:** Resolved
- **Features:** Multiple victims (3), multiple suspects (2)
- **Demonstrates:** Complex multi-party incident handling

### 3. `incident_report_MURDER-001.html` - Homicide Investigation
- **Type:** Homicide investigation
- **Status:** Under Investigation
- **Features:** Comprehensive analysis, crime scene details, suspect prioritization
- **Demonstrates:** Advanced AI analysis capabilities, professional law enforcement formatting

## How to Convert to PDF

### Method 1: Browser Print Function
1. Open any HTML file in a web browser (Chrome, Firefox, Safari, Edge)
2. Press `Ctrl+P` (Windows/Linux) or `Cmd+P` (Mac)
3. Select "Save as PDF" as the destination
4. Choose appropriate settings:
   - **Layout:** Portrait
   - **Paper size:** A4 or Letter
   - **Margins:** Default
   - **Options:** Include headers and footers
5. Click "Save" and choose your destination

### Method 2: Browser Developer Tools
1. Open the HTML file in Chrome
2. Press `F12` to open Developer Tools
3. Press `Ctrl+Shift+P` and type "PDF"
4. Select "Print to PDF"
5. The PDF will be automatically generated

### Method 3: Online HTML to PDF Converters
- Use services like HTML/CSS to PDF API
- Upload the HTML file
- Download the generated PDF

## Features Demonstrated

### Professional Formatting
- Clean, professional layout suitable for law enforcement
- Proper typography and spacing
- Print-optimized CSS styles
- Consistent branding and headers

### Dynamic Content Structure
- **Header Section:** Report ID, generation timestamp, classification
- **Incident Details:** Structured table with all key information
- **Victims Section:** Individual cards for each victim with detailed information
- **Suspects Section:** Individual cards for each suspect with descriptions
- **Evidence Section:** Comprehensive evidence listing
- **AI Analysis:** Detailed analysis with recommendations

### Responsive Design
- Optimized for both screen viewing and printing
- Proper page breaks for multi-page reports
- Consistent formatting across different browsers

### Data Integration
- Supports the exact JSON structure from your incidents.json
- Dynamic victim and suspect handling (1 to many)
- Flexible evidence and analysis sections
- Status badges with appropriate color coding

## Technical Implementation

### CSS Features
- Professional color scheme (#003366 for headers)
- Table styling with alternating row colors
- Status badges with color coding
- Print-specific media queries
- Responsive layout design

### Data Structure Support
```json
{
  "id": "Report ID",
  "date": "Incident date",
  "time": "Incident time", 
  "location": "Location details",
  "incident_type": "Type of incident",
  "description": "Detailed description",
  "reporting_officer": "Officer name",
  "evidence": "Evidence summary",
  "status": "Current status",
  "victims": [
    {
      "name": "Victim name",
      "age": "Age",
      "gender": "Gender",
      "injuries": "Injury details"
    }
  ],
  "suspects": [
    {
      "name": "Suspect name", 
      "age": "Age",
      "gender": "Gender",
      "description": "Physical description"
    }
  ],
  "ai_analysis": "AI-generated analysis"
}
```

## Integration with Chat System

These reports are automatically generated when:
1. A crime agent completes an investigation
2. The user clicks "Download PDF Report" 
3. The system extracts data from the chat context
4. A professional report is generated and downloaded

## Quality Assurance

Each generated report includes:
- ✅ Professional formatting
- ✅ Complete data integration
- ✅ Print optimization
- ✅ Consistent branding
- ✅ Proper information hierarchy
- ✅ Legal compliance formatting
- ✅ Multi-page support
- ✅ Cross-browser compatibility

## Next Steps

To implement this in production:
1. Install Python dependencies: `pip install reportlab flask flask-cors`
2. Start the Python backend: `python unified_server.py`
3. Test the PDF generation endpoint
4. Integrate with the Next.js frontend
5. Deploy and test in production environment

The system provides a significant upgrade from the previous JavaScript-based PDF generation, offering professional-quality reports suitable for law enforcement and legal proceedings.
