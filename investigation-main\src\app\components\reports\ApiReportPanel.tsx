/* eslint-disable */
'use client';

import React, { useState } from 'react';
import { ApiReport } from '@/app/types';
import Card, { CardHeader, CardTitle, CardContent } from '@/app/components/ui/Card';
import DesignatedPanel from './DesignatedPanel';
import {
  DocumentTextIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationCircleIcon,
} from '@heroicons/react/24/outline';

interface ApiReportPanelProps {
  report: ApiReport;
  expanded?: boolean;
}

const ApiReportPanel: React.FC<ApiReportPanelProps> = ({ report, expanded = false }) => {
  const [isExpanded, setIsExpanded] = useState(expanded);

  // Get status color
  const getStatusColor = () => {
    switch (report.status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get status icon
  const getStatusIcon = () => {
    switch (report.status) {
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-yellow-600" />;
      case 'error':
        return <ExclamationCircleIcon className="h-5 w-5 text-red-600" />;
      default:
        return <DocumentTextIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  return (
    <Card className="overflow-hidden">
      <div 
        className="flex items-center justify-between p-4 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center">
          <DocumentTextIcon className="h-6 w-6 text-blue-600 mr-3" />
          <div>
            <h3 className="font-medium text-gray-900">{report.title}</h3>
            <div className="flex items-center text-sm text-gray-500">
              <span className="mr-2">Case ID: {report.caseId}</span>
              <span className="mr-2">•</span>
              <span>Generated: {new Date(report.generatedDate).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${getStatusColor()}`}>
            <span className="mr-1">{getStatusIcon()}</span>
            {report.status.charAt(0).toUpperCase() + report.status.slice(1)}
          </span>
          {isExpanded ? (
            <ChevronUpIcon className="h-5 w-5 text-gray-400" />
          ) : (
            <ChevronDownIcon className="h-5 w-5 text-gray-400" />
          )}
        </div>
      </div>
      
      {isExpanded && (
        <CardContent className="border-t border-gray-200 bg-gray-50 pt-4">
          <div className="mb-4">
            <p className="text-sm text-gray-500">
              Generated by: <span className="font-medium text-gray-700">{report.generatedBy}</span>
            </p>
            <p className="text-sm text-gray-500">
              Case Type: <span className="font-medium text-gray-700">{report.caseType}</span>
            </p>
          </div>
          
          <h4 className="font-medium text-gray-700 mb-3">Designated Panels</h4>
          <div className="space-y-4">
            {report.panels.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No panels available</p>
            ) : (
              report.panels.map((panel) => (
                <DesignatedPanel key={panel.id} panel={panel} />
              ))
            )}
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export default ApiReportPanel;
